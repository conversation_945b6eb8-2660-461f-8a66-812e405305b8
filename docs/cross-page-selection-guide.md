# CgGrid 跨页勾选功能使用指南

## 功能概述

CgGrid 组件现已支持跨页勾选功能，允许用户在分页表格中跨页面保持选中状态。当用户切换页面时，之前选中的数据仍然保持选中状态。

## 核心配置

要启用跨页勾选功能，需要在 `options` 中配置两个关键属性：

```typescript
const gridOptions = {
  options: {
    // ① 设置唯一主键字段（必须）
    rowConfig: { keyField: 'id' },
    
    // ② 开启跨页缓存（必须）
    checkboxConfig: { reserve: true },
    
    columns: [
      // 你的列配置...
    ]
  },
  // 启用复选框
  selection: [{ type: 'checkbox' }]
}
```

### 配置说明

| 配置项 | 作用 | 注意事项 |
|--------|------|----------|
| `rowConfig.keyField` | 指定每行数据的唯一标识字段 | 必须是数据中存在且唯一的字段，如 `id`、`uuid` 等 |
| `checkboxConfig.reserve` | 启用跨页选中状态缓存 | 设置为 `true` 即可启用 |

## API 方法

### 获取选中数据

```typescript
// 获取当前页选中的数据
const currentPageSelected = pageListRef.value?.getCheckboxRecords()

// 获取跨页选中的所有数据（推荐）
const allPageSelected = pageListRef.value?.getCheckboxReserveRecords()

// 获取选中数据的ID列表
const selectedIds = allPageSelected?.map(row => row.id) || []
```

### 清空选中状态

```typescript
// 清空所有页面的选中状态
pageListRef.value?.clearCheckboxReserve()
```

## 使用示例

### 1. 基础使用（CgPageList）

```vue
<script setup lang="ts">
import type { PageListInstance } from '@/components/CgPageList'

const pageListRef = ref<PageListInstance | null>(null)

const gridOptions = reactive({
  gridOption: {
    options: {
      // 🔑 关键配置
      rowConfig: { keyField: 'id' },
      checkboxConfig: { reserve: true },
      columns: [
        { field: 'id', title: 'ID', width: 80 },
        { field: 'name', title: '名称', minWidth: 150 },
        // 更多列...
      ]
    },
    selection: [{ type: 'checkbox' }],
    proxyOption: {
      request: yourApiFunction
    }
  }
})

// 获取跨页选中数据
function getSelectedData() {
  const selected = pageListRef.value?.getCheckboxReserveRecords() || []
  console.log('选中的数据:', selected)
}
</script>

<template>
  <CgPageList 
    ref="pageListRef" 
    v-model:query-model="queryModel" 
    v-bind="gridOptions"
  />
</template>
```

### 2. 直接使用 CgGrid

```vue
<script setup lang="ts">
import type { GridInstance } from '@/components/CgGrid'

const gridRef = ref<GridInstance | null>(null)

const gridOptions = {
  rowConfig: { keyField: 'id' },
  checkboxConfig: { reserve: true },
  columns: [
    // 列配置...
  ]
}

function getSelectedData() {
  const selected = gridRef.value?.getCheckboxReserveRecords() || []
  console.log('选中的数据:', selected)
}
</script>

<template>
  <CgGrid 
    ref="gridRef"
    :options="gridOptions"
    :selection="[{ type: 'checkbox' }]"
    :proxy-option="proxyOption"
  />
</template>
```

## 实际应用场景

### 批量操作

```typescript
function batchDelete() {
  const selected = pageListRef.value?.getCheckboxReserveRecords() || []
  
  if (selected.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selected.length} 条数据吗？`,
    '批量删除确认',
    { type: 'warning' }
  ).then(async () => {
    const selectedIds = selected.map(row => row.id)
    await batchDeleteApi(selectedIds)
    
    // 清空选中状态并刷新数据
    pageListRef.value?.clearCheckboxReserve()
    pageListRef.value?.reload()
    
    ElMessage.success('批量删除成功')
  })
}
```

### 批量导出

```typescript
function batchExport() {
  const selected = pageListRef.value?.getCheckboxReserveRecords() || []
  
  if (selected.length === 0) {
    ElMessage.warning('请先选择要导出的数据')
    return
  }
  
  const selectedIds = selected.map(row => row.id)
  
  // 调用导出API
  exportDataByIds(selectedIds)
}
```

## 注意事项

1. **主键字段要求**
   - `keyField` 必须指向数据中真实存在的字段
   - 该字段的值必须在所有数据中唯一
   - 推荐使用 `id`、`uuid` 等数据库主键字段

2. **性能考虑**
   - 跨页选中的数据会保存在内存中
   - 对于大量数据的选择，建议提供"全选当前筛选条件"的功能

3. **数据一致性**
   - 当数据发生变化（如删除、更新）时，建议调用 `clearCheckboxReserve()` 清空选中状态
   - 或者在数据刷新后重新验证选中数据的有效性

4. **用户体验**
   - 建议在界面上显示当前选中的数据数量
   - 提供"清空选中"按钮方便用户操作

## 常见问题

### Q: 为什么切换页面后选中状态丢失了？
A: 检查是否正确配置了 `rowConfig.keyField` 和 `checkboxConfig.reserve`。

### Q: 如何限制最大选择数量？
A: 可以在获取选中数据时进行验证：

```typescript
function checkSelectionLimit() {
  const selected = pageListRef.value?.getCheckboxReserveRecords() || []
  const maxLimit = 100
  
  if (selected.length > maxLimit) {
    ElMessage.warning(`最多只能选择 ${maxLimit} 条数据`)
    return false
  }
  return true
}
```

### Q: 如何在数据更新后保持选中状态？
A: 只要 `keyField` 对应的值不变，选中状态会自动保持。如果数据结构发生变化，可能需要重新设置选中状态。

## 更新日志

- **v1.0.0**: 新增跨页勾选功能支持
  - 新增 `getCheckboxReserveRecords()` 方法
  - 新增 `clearCheckboxReserve()` 方法
  - 支持通过 `rowConfig` 和 `checkboxConfig` 配置跨页选中
